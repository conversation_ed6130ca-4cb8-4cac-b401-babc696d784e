/**
 * Utility functions for Vibe Coding workflow system
 */

import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { WorkflowConfig, SubAgentsConfig } from "../types.js";


const vibeDir = '.vibecode';

export const vibePath = (path) => path.join(path, vibeDir);

export const getSteeringDir = (rootPath) => path.join(vibePath(rootPath), "steering");
export const getSpecsDir = (rootPath) => path.join(vibePath(rootPath), "specs");
export const getBugsDir = (rootPath) => path.join(vibePath(rootPath), "bugs");;


/**
 * Ensure workflow directories exist
 */
export async function ensureWorkflowDirectories(rootPath: string): Promise<void> {
  const workflowDirs = [
    getSteeringDir(rootPath),
    getSpecsDir(rootPath),
    getBugsDir(rootPath),
  ];

  for (const dir of workflowDirs) {
    await fs.mkdir(dir, { recursive: true });
  }
}

/**
 * Load workflow configuration
 */
export async function loadWorkflowConfig(rootPath: string): Promise<WorkflowConfig> {
  const vibecodePath = vibePath(rootPath);
  const configPath = path.join(vibecodePath, "config.json");

  try {
    const content = await fs.readFile(configPath, "utf-8");
    const config = JSON.parse(content);

    // Ensure config has required structure
    return {
      specs: config.specs || {},
      bugs: config.bugs || {},
      subAgents: config.subAgents || undefined,
      ...config
    };
  } catch (error) {
    // If config file doesn't exist or is invalid, return default config
    const defaultConfig: WorkflowConfig = {
      specs: {},
      bugs: {}
    };

    // Ensure .vibecode directory exists before saving
    if (!existsSync(vibecodePath)) {
      await fs.mkdir(vibecodePath, { recursive: true });
    }

    await saveWorkflowConfig(rootPath, defaultConfig);
    return defaultConfig;
  }
}

/**
 * Save workflow configuration
 */
export async function saveWorkflowConfig(rootPath: string, config: WorkflowConfig): Promise<void> {
  const vibecodePath = vibePath(rootPath);

  const configPath = path.join(vibecodePath, "config.json");

  // Ensure .vibecode directory exists
  if (!existsSync(vibecodePath)) {
    await fs.mkdir(vibecodePath, { recursive: true });
  }

  // Validate config structure before saving
  const validatedConfig: WorkflowConfig = {
    specs: config.specs || {},
    bugs: config.bugs || {},
    ...config
  };

  try {
    await fs.writeFile(configPath, JSON.stringify(validatedConfig, null, 2), "utf-8");
  } catch (error) {
    throw new Error(`Failed to save workflow config: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Generate spec name from title
 */
export function generateSpecName(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * Get current timestamp
 */
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Load Sub-Agents configuration from .vibecode/config.json
 */
export async function loadSubAgentsConfig(rootPath: string): Promise<SubAgentsConfig> {
  const config = await loadWorkflowConfig(rootPath);

  if (config.subAgents) {
    return {
      qualityThreshold: config.subAgents.qualityThreshold || 95,
      outputFormat: config.subAgents.outputFormat || "detailed",
      autoRestart: config.subAgents.autoRestart !== false,
      maxRetries: config.subAgents.maxRetries || 3,
      maxWorkflowRetries: config.subAgents.maxWorkflowRetries || 2,
      enabledAgents: config.subAgents.enabledAgents || ["spec", "architect", "developer", "quality", "test"],
      phaseThresholds: config.subAgents.phaseThresholds || {
        implementation: 85,
        quality: 75,
        testing: 70
      },
      saveResults: config.subAgents.saveResults !== false,
      showProgress: config.subAgents.showProgress !== false
    };
  }

  // Return default Sub-Agents configuration if not found
  const defaultSubAgentsConfig: SubAgentsConfig = {
    qualityThreshold: 95,
    outputFormat: "detailed",
    autoRestart: true,
    maxRetries: 3,
    maxWorkflowRetries: 2,
    enabledAgents: ["spec", "architect", "developer", "quality", "test"],
    phaseThresholds: {
      implementation: 85,
      quality: 75,
      testing: 70
    },
    saveResults: true,
    showProgress: true
  };

  return defaultSubAgentsConfig;
}

/**
 * Update specific spec configuration
 */
export async function updateSpecConfig(rootPath: string, specName: string, updates: Partial<any>): Promise<void> {
  const config = await loadWorkflowConfig(rootPath);

  if (!config.specs[specName]) {
    throw new Error(`Specification "${specName}" not found in configuration`);
  }

  config.specs[specName] = {
    ...config.specs[specName],
    ...updates,
    updatedAt: getCurrentTimestamp()
  };

  await saveWorkflowConfig(rootPath, config);
}

/**
 * Update specific bug configuration
 */
export async function updateBugConfig(rootPath: string, bugName: string, updates: Partial<any>): Promise<void> {
  const config = await loadWorkflowConfig(rootPath);

  if (!config.bugs[bugName]) {
    throw new Error(`Bug "${bugName}" not found in configuration`);
  }

  config.bugs[bugName] = {
    ...config.bugs[bugName],
    ...updates,
    updatedAt: getCurrentTimestamp()
  };

  await saveWorkflowConfig(rootPath, config);
}

/**
 * Add new spec to configuration
 */
export async function addSpecToConfig(rootPath: string, specName: string, specConfig: any): Promise<void> {
  const config = await loadWorkflowConfig(rootPath);

  if (config.specs[specName]) {
    throw new Error(`Specification "${specName}" already exists in configuration`);
  }

  config.specs[specName] = {
    ...specConfig,
    createdAt: getCurrentTimestamp(),
    updatedAt: getCurrentTimestamp()
  };

  await saveWorkflowConfig(rootPath, config);
}

/**
 * Add new bug to configuration
 */
export async function addBugToConfig(rootPath: string, bugName: string, bugConfig: any): Promise<void> {
  const config = await loadWorkflowConfig(rootPath);

  if (config.bugs[bugName]) {
    throw new Error(`Bug "${bugName}" already exists in configuration`);
  }

  config.bugs[bugName] = {
    ...bugConfig,
    createdAt: getCurrentTimestamp(),
    updatedAt: getCurrentTimestamp()
  };

  await saveWorkflowConfig(rootPath, config);
}

/**
 * Remove spec from configuration
 */
export async function removeSpecFromConfig(rootPath: string, specName: string): Promise<void> {
  const config = await loadWorkflowConfig(rootPath);

  if (!config.specs[specName]) {
    throw new Error(`Specification "${specName}" not found in configuration`);
  }

  delete config.specs[specName];
  await saveWorkflowConfig(rootPath, config);
}

/**
 * Remove bug from configuration
 */
export async function removeBugFromConfig(rootPath: string, bugName: string): Promise<void> {
  const config = await loadWorkflowConfig(rootPath);

  if (!config.bugs[bugName]) {
    throw new Error(`Bug "${bugName}" not found in configuration`);
  }

  delete config.bugs[bugName];
  await saveWorkflowConfig(rootPath, config);
}

/**
 * Get all specs from configuration
 */
export async function getAllSpecs(rootPath: string): Promise<Record<string, any>> {
  const config = await loadWorkflowConfig(rootPath);
  return config.specs || {};
}

/**
 * Get all bugs from configuration
 */
export async function getAllBugs(rootPath: string): Promise<Record<string, any>> {
  const config = await loadWorkflowConfig(rootPath);
  return config.bugs || {};
}

/**
 * Get specific spec configuration
 */
export async function getSpecConfig(rootPath: string, specName: string): Promise<any | null> {
  const config = await loadWorkflowConfig(rootPath);
  return config.specs[specName] || null;
}

/**
 * Get specific bug configuration
 */
export async function getBugConfig(rootPath: string, bugName: string): Promise<any | null> {
  const config = await loadWorkflowConfig(rootPath);
  return config.bugs[bugName] || null;
}

/**
 * Check if spec exists in configuration
 */
export async function specExists(rootPath: string, specName: string): Promise<boolean> {
  const config = await loadWorkflowConfig(rootPath);
  return !!config.specs[specName];
}

/**
 * Check if bug exists in configuration
 */
export async function bugExists(rootPath: string, bugName: string): Promise<boolean> {
  const config = await loadWorkflowConfig(rootPath);
  return !!config.bugs[bugName];
}

/**
 * Parse .gitignore file
 */
export async function parseGitignore(
  rootPath: string,
  targetPath: string
): Promise<boolean | null> {
  const gitignorePath = path.join(rootPath, ".gitignore");

  // Check if .gitignore file exists
  if (!existsSync(gitignorePath)) {
    return null;
  }

  try {
    // Read .gitignore file content
    const content = await fs.readFile(gitignorePath, "utf-8");
    // Use gitignore-parser's compile method to parse .gitignore content
    const gitignoreParser = await import("gitignore-parser");
    const gitignore = gitignoreParser.compile(content);

    // Use denies method to check if path is rejected (ignored)
    return gitignore.denies(targetPath);
  } catch (error) {
    console.error("Error parsing .gitignore:", error);
    return null;
  }
}
