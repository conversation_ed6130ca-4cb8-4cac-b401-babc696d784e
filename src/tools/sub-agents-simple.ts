/**
 * Sub-Agents MCP tools - Professional AI team workflow system
 * Based on Claude Code Sub-Agents methodology with quality gates
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { z } from "zod";
import { ROOT_PATH_DESC } from "../utils/const.js";

/**
 * Register Sub-Agents tools - Professional AI team workflow system
 */
export function registerSubAgentsTools(server: McpServer): void {
  // 主要的 vibe-coding 工具 - 一键式自动化开发流程
  server.tool(
    "vibe-coding",
    "🚀 一键式自动化开发流程：只需一个命令完成 规格生成 → 代码实现 → 质量验收 → 测试生成，全程95%质量门控，完全无人工干预",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      task: z.string().describe("开发任务描述，例如：'开发用户认证管理系统'"),
      qualityThreshold: z.number().min(80).max(100).default(95).describe("质量阈值百分比"),
      outputFormat: z.enum(["detailed", "summary", "json"]).default("detailed").describe("输出格式"),
    },
    async ({ rootPath, task, qualityThreshold = 95, outputFormat = "detailed" }) => {
      try {
        console.log(`🚀 启动 Vibe Coding 自动化开发流程: ${task}`);
        console.log(`☕ 请喝杯咖啡，看着AI专家团队自动完成开发！`);
        console.log(`🎯 质量目标: ${qualityThreshold}%`);

        // 初始化项目结构
        await ensureProjectStructure(rootPath);

        // 生成特性名称
        const featureName = generateFeatureName(task);

        // 执行自动化开发流程
        const result = await executeAutomatedWorkflow(rootPath, featureName, task, qualityThreshold);

        // 格式化输出
        return formatWorkflowResult(result, outputFormat);

      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 自动化开发流程失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Spec Generation Agent - 规格生成代理
  server.tool(
    "spec-generation",
    "📋 规格生成代理：基于任务描述自动生成完整的需求文档、设计文档和任务清单",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      featureDescription: z.string().describe("功能描述"),
      featureName: z.string().optional().describe("功能名称（可选，自动生成）")
    },
    async ({ rootPath, featureDescription, featureName }) => {
      try {
        const name = featureName || generateFeatureName(featureDescription);
        const specs = await generateSpecifications(featureDescription, name);

        // 保存规格文档
        await saveSpecifications(rootPath, name, specs);

        return {
          content: [
            {
              type: "text",
              text: `✅ 规格生成完成: ${name}\n\n📋 生成的文档:\n- requirements.md\n- design.md\n- tasks.md\n\n🎯 下一步: 运行 spec-executor 开始实现`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 规格生成失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Spec Executor Agent - 代码实现代理
  server.tool(
    "spec-executor",
    "💻 代码实现代理：基于规格文档自动实现代码，包含完整的进度跟踪",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      featureName: z.string().describe("要实现的功能名称")
    },
    async ({ rootPath, featureName }) => {
      try {
        // 检查规格文档是否存在
        const specsPath = path.join(rootPath, ".vibecode", "specs", featureName);
        if (!existsSync(specsPath)) {
          return {
            content: [
              {
                type: "text",
                text: `❌ 规格文档不存在: ${featureName}。请先运行 spec-generation`,
              },
            ],
          };
        }

        // 执行代码实现
        const implementation = await executeImplementation(rootPath, featureName);

        return {
          content: [
            {
              type: "text",
              text: `✅ 代码实现完成: ${featureName}\n\n💻 实现内容:\n${implementation.summary}\n\n🎯 下一步: 运行 spec-validation 进行质量验证`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 代码实现失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Spec Validation Agent - 质量验证代理
  server.tool(
    "spec-validation",
    "🔍 质量验证代理：多维度代码质量评估，量化评分(0-100%)，95%质量门控",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      featureName: z.string().describe("要验证的功能名称"),
      qualityThreshold: z.number().min(80).max(100).default(95).describe("质量阈值")
    },
    async ({ rootPath, featureName, qualityThreshold = 95 }) => {
      try {
        // 执行质量验证
        const validation = await performValidation(rootPath, featureName);

        // 保存验证报告
        await saveValidationReport(rootPath, featureName, validation);

        const passed = validation.score >= qualityThreshold;
        const decision = passed ?
          "✅ 质量验证通过，准备进入测试阶段" :
          `⚠️ 质量未达标，需要改进。当前分数: ${validation.score}%，目标: ${qualityThreshold}%`;

        return {
          content: [
            {
              type: "text",
              text: `🔍 质量验证完成: ${featureName}\n\n📊 质量分数: ${validation.score}/100\n\n🎯 验证结果: ${decision}\n\n${passed ? '✅ 下一步: 运行 spec-testing 生成测试' : '🔄 建议: 重新运行 spec-generation 优化代码'}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 质量验证失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Spec Testing Agent - 测试生成代理
  server.tool(
    "spec-testing",
    "🧪 测试生成代理：为验证通过的代码生成综合测试套件",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      featureName: z.string().describe("要测试的功能名称")
    },
    async ({ rootPath, featureName }) => {
      try {
        // 生成测试套件
        const testing = await generateTestSuite(rootPath, featureName);

        // 保存测试报告
        await saveTestingReport(rootPath, featureName, testing);

        return {
          content: [
            {
              type: "text",
              text: `🧪 测试生成完成: ${featureName}\n\n📊 测试统计:\n- 单元测试: ${testing.stats.unitTests}\n- 集成测试: ${testing.stats.integrationTests}\n- 安全测试: ${testing.stats.securityTests}\n- 性能测试: ${testing.stats.performanceTests}\n\n✅ 功能 "${featureName}" 开发完成，具备完整测试覆盖！`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 测试生成失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Workflow Status Tool - 工作流状态查询
  server.tool(
    "vibe-coding-status",
    "📊 查看 Vibe Coding 自动化开发流程的当前状态",
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
      featureName: z.string().optional().describe("特定功能名称（可选）")
    },
    async ({ rootPath, featureName }) => {
      try {
        const status = await getWorkflowStatus(rootPath, featureName);
        return {
          content: [
            {
              type: "text",
              text: status,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 状态查询失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}

/**
 * 确保项目结构存在
 */
async function ensureProjectStructure(rootPath: string): Promise<void> {
  const vibePath = path.join(rootPath, ".vibecode");
  const specsPath = path.join(vibePath, "specs");

  if (!existsSync(vibePath)) {
    await fs.mkdir(vibePath, { recursive: true });
  }

  if (!existsSync(specsPath)) {
    await fs.mkdir(specsPath, { recursive: true });
  }
}

/**
 * 生成功能名称
 */
function generateFeatureName(description: string): string {
  return description
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .substring(0, 50);
}

/**
 * 执行自动化工作流程
 */
async function executeAutomatedWorkflow(
  rootPath: string,
  featureName: string,
  task: string,
  qualityThreshold: number
): Promise<any> {
  const startTime = Date.now();
  const results = [];

  console.log(`🔄 开始自动化开发流程...`);

  // 阶段1: 规格生成
  console.log(`📋 阶段1: 规格生成`);
  const specs = await generateSpecifications(task, featureName);
  await saveSpecifications(rootPath, featureName, specs);
  results.push({ stage: 'generation', success: true, output: specs });

  // 阶段2: 代码实现
  console.log(`💻 阶段2: 代码实现`);
  const implementation = await executeImplementation(rootPath, featureName);
  results.push({ stage: 'implementation', success: true, output: implementation });

  // 阶段3: 质量验证（带自动优化循环）
  console.log(`🔍 阶段3: 质量验证`);
  let validation;
  let attempts = 0;
  const maxAttempts = 3;

  do {
    attempts++;
    console.log(`🔄 质量验证尝试 ${attempts}/${maxAttempts}`);

    validation = await performValidation(rootPath, featureName);
    await saveValidationReport(rootPath, featureName, validation);

    if (validation.score >= qualityThreshold) {
      console.log(`✅ 质量验证通过: ${validation.score}%`);
      break;
    } else {
      console.log(`⚠️ 质量未达标: ${validation.score}%，需要优化`);
      if (attempts < maxAttempts) {
        // 自动优化：重新生成规格和实现
        console.log(`🔄 自动优化中...`);
        const optimizedSpecs = await optimizeSpecifications(task, featureName, validation.issues);
        await saveSpecifications(rootPath, featureName, optimizedSpecs);
        const optimizedImpl = await executeImplementation(rootPath, featureName);
      }
    }
  } while (validation.score < qualityThreshold && attempts < maxAttempts);

  results.push({ stage: 'validation', success: validation.score >= qualityThreshold, output: validation });

  // 阶段4: 测试生成（仅在质量验证通过时）
  if (validation.score >= qualityThreshold) {
    console.log(`🧪 阶段4: 测试生成`);
    const testing = await generateTestSuite(rootPath, featureName);
    await saveTestingReport(rootPath, featureName, testing);
    results.push({ stage: 'testing', success: true, output: testing });
  }

  const totalTime = Date.now() - startTime;
  console.log(`✅ 自动化开发流程完成，耗时: ${Math.round(totalTime / 1000)}秒`);

  return {
    featureName,
    task,
    qualityThreshold,
    finalQuality: validation.score,
    attempts,
    totalTime,
    results,
    success: validation.score >= qualityThreshold
  };
}

/**
 * 格式化工作流结果
 */
function formatWorkflowResult(result: any, format: string): any {
  if (format === "json") {
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  }

  const summary = format === "detailed" ?
    `🎉 Vibe Coding 自动化开发流程完成！

🚀 项目: ${result.task}
📋 功能: ${result.featureName}
📊 最终质量: ${result.finalQuality}% (目标: ${result.qualityThreshold}%)
🔄 优化轮次: ${result.attempts}
⏱️ 总耗时: ${Math.round(result.totalTime / 1000)}秒
✅ 状态: ${result.success ? '成功完成' : '需要人工干预'}

📋 完成阶段:
${result.results.map((r: any) =>
      `${r.success ? '✅' : '❌'} ${r.stage}: ${r.success ? '完成' : '失败'}`
    ).join('\n')}

${result.success ?
      '🎯 恭喜！您的功能已完成开发，包含完整的规格文档、代码实现、质量验证和测试套件！' :
      '⚠️ 开发流程未完全成功，请检查质量验证结果并考虑手动优化。'
    }` :
    `${result.success ? '✅' : '❌'} ${result.featureName}: ${result.finalQuality}% 质量 (${Math.round(result.totalTime / 1000)}秒)`;

  return {
    content: [
      {
        type: "text",
        text: summary,
      },
    ],
  };
}
