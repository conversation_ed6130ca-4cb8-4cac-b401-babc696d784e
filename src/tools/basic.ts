/**
 * Basic MCP tools for project information management
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { exec } from "child_process";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { promisify } from "util";
import { z } from "zod";
import { ensureWorkflowDirectories, saveWorkflowConfig, vibePath } from "../utils/utils.js";
import { WorkflowConfig } from "../types.js";
import { getFileTree } from "../utils/fileTree.js";
import { ROOT_PATH_DESC } from "../utils/const.js";

const execPromise = promisify(exec);

const GET_PROJECT_TEMPLATE = `
This is the current project details, include project structure, dev attentions, and other important information:

{{S}}

KEEP IN MIND:
- After you finish modifying code to satisfy user requirements, you have to call 'update-project-info' which help you ensure the document remains up to date.
- Follow the response of 'update-project-info' to update .vibecode/*.md files.
`;

/**
 * Register basic MCP tools
 */
export function registerBasicTools(server: McpServer): void {
  // Get project info tool
  server.tool(
    "get-project-info",
    `Complete the project details and points to note.
Its very important for LLM/Agent edit code. The more you know, the more you can do.
Its very useful for cursor or windsurf no matter in agent or edit mode.
**Highly recommended for use under all circumstances**.`,
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
    },
    async ({ rootPath }) => {
      const content = await fs
        .readdir(path.join(vibePath(rootPath)))
        .then(async (files) => {
          const mdFiles = files.filter((f) => f.endsWith(".md"));
          const contents = await Promise.all(
            mdFiles.map(async (file) => {
              // ignore files start with "_", like _changelog.md
              if (file.startsWith("_")) {
                return "";
              }
              const content = await fs.readFile(
                path.join(vibePath(rootPath), file),
                "utf-8"
              );
              const name = path.basename(file, ".md");
              return `<${name}>\n\n${content}\n\n</${name}>\n`;
            })
          );
          return GET_PROJECT_TEMPLATE.replace("{{S}}", contents.join("\n"));
        })
        .catch(() => "");
      return {
        content: [
          {
            type: "text",
            text: content,
          },
        ],
      };
    }
  );

  // Update project info tool
  server.tool(
    "update-project-info",
    "When you have finished modifying code to satisfy user requirements, you have to update .vibecode/*.md files. This tool helps you ensure the document remains up to date.",
    {
      rootPath: z.string().describe(
        `The root path of the project,
         "C:/User/name/codeProject" in windows
         "/usr/name/codeProject/" in macos/linux`
      ),
    },
    async ({ rootPath }) => {
      return {
        content: [
          {
            type: "text",
            text: `[ATTENTION] Next step you must do:
** Read files .vibecode/project.md and .vibecode/changelog.md and update them based on the changes you have just done.**`,
          },
        ],
      };
    }
  );

  // Initialize vibe tool
  server.tool(
    "init-vibe",
    `Initialize .vibecode directory and files which can help LLM better understand your project.

After init .vibecode directory and files, you should:
1. Every file in .vibecode directory is a markdown file, you can read them and update them.
2. You HAVE TO follow the instructions in .vibecode/*.md files and update them.`,
    {
      rootPath: z.string().describe(ROOT_PATH_DESC),
    },
    async ({ rootPath }) => {
      try {
        await fs.mkdir(path.join(vibePath(rootPath)), { recursive: true });

        // Check if target directory is empty
        const files = await fs.readdir(path.join(vibePath(rootPath)));

        if (files.length === 0) {
          // Directory is empty, execute git clone
          const { stdout, stderr } = await execPromise(
            `git clone https://github.com/Disdjj/codelf-template ${path.join(vibePath(rootPath))}`
          );

          // Remove .git folder
          await fs.rm(path.join(vibePath(rootPath), ".git"), {
            recursive: true,
          });
          const fileTree = await getFileTree(rootPath);

          // Append file tree to .vibecode/project.md
          await fs.appendFile(
            path.join(vibePath(rootPath), "project.md"),
            `\n\`\`\`\n${fileTree}\n\`\`\`\n`
          );

          // Initialize workflow directories
          await ensureWorkflowDirectories(rootPath);

          // Create default workflow configuration
          const defaultConfig: WorkflowConfig = {
            specs: {},
            bugs: {},
          };
          await saveWorkflowConfig(rootPath, defaultConfig);

          return {
            content: [
              {
                type: "text",
                text: `Successfully initialized .vibecode directory with template.\nOutput: ${stdout}\n${stderr ? `Error: ${stderr}` : ""
                  }`,
              },
              {
                type: "text",
                text: `✅ Vibe Coding workflow system initialized!

📁 Created directory structure:
- .vibecode/steering/ (Steering documents)
- .vibecode/specs/ (Spec workflow)
- .vibecode/bugs/ (Bug fix workflow)
- .vibecode/templates/ (Template files)

🚀 Available workflow commands:

**Steering System:**
- init-steering - Initialize Steering documents
- get-steering - Get Steering information

**Spec Workflow:**
- spec-create - Create new specification
- spec-requirements - Generate requirements document
- spec-status - View specification status
- spec-list - List all specifications

**Bug Fix Workflow:**
- bug-create - Create bug report
- bug-status - View bug status

[Attention]
Next step you should do:
1. Every file in .vibecode directory is a markdown file, you can read them and update them.
2. You HAVE TO follow the instructions in .vibecode/*.md files and update them.
3. Before you finish edit .vibecode/*.md files, do not use update-project-info/get-project-info.
4. After you finish edit .vibecode/*.md files, just stop.
5. Recommend running 'init-steering' to initialize Steering document system.`,
              },
            ],
          };
        } else {
          // Directory is not empty, prompt user
          return {
            content: [
              {
                type: "text",
                text: "The .vibecode directory already exists and is not empty. Please remove or empty it before initializing.",
              },
            ],
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Failed to initialize .vibecode directory: ${error instanceof Error ? error.message : String(error)
                }`,
            },
          ],
        };
      }
    }
  );
}
